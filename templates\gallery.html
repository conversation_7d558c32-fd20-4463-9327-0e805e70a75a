<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>画廊 - 梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .gallery-card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out;
        }
        .gallery-card:hover {
            transform: translateY(-2px);
        }
        .gallery-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
        }
        .gallery-video {
            width: 100%;
            height: 300px;
            border-radius: 5px;
        }
        .placeholder-image {
            width: 100%;
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            border-radius: 5px;
            font-size: 14px;
        }
        .prompt-text {
            font-size: 0.9em;
            color: #666;
            max-height: 120px;
            overflow-y: auto;
            word-wrap: break-word;
            white-space: pre-wrap;
            line-height: 1.4;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .negative-prompt-text {
            font-size: 0.8em;
            color: #999;
            max-height: 80px;
            overflow-y: auto;
            word-wrap: break-word;
            white-space: pre-wrap;
            line-height: 1.3;
            padding: 6px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .user-info {
            font-size: 0.85em;
            color: #007bff;
        }
        .time-info {
            font-size: 0.8em;
            color: #6c757d;
        }
        .model-info {
            font-size: 0.8em;
            color: #28a745;
        }
        .no-records {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .gallery-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .card-img-container {
            position: relative;
        }

        /* 图片预览模态框样式 */
        .modal-dialog {
            max-width: 90vw;
            max-height: 90vh;
        }

        .modal-content {
            max-height: 90vh;
            overflow: hidden;
        }

        .modal-body {
            max-height: calc(90vh - 120px);
            overflow: auto;
            padding: 0;
        }

        #modalImage {
            max-width: 100%;
            max-height: calc(90vh - 120px);
            width: auto;
            height: auto;
            object-fit: contain;
        }

        /* 提示词展开/收起功能 */
        .prompt-toggle {
            cursor: pointer;
            color: #007bff;
            font-size: 0.8em;
            text-decoration: underline;
            margin-top: 4px;
        }

        .prompt-toggle:hover {
            color: #0056b3;
        }

        .prompt-text.collapsed {
            max-height: 80px;
            overflow: hidden;
        }

        .negative-prompt-text.collapsed {
            max-height: 60px;
            overflow: hidden;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 30px;
        }

        .pagination .page-link {
            color: #333;
            border-color: #ddd;
        }

        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            color: #aaa;
        }

        /* 搜索框样式 */
        #searchInput {
            border-right: none;
        }

        #clearSearch {
            border-left: none;
            background-color: white;
        }

        #clearSearch:hover {
            background-color: #f8f9fa;
        }

        /* 加载指示器样式 */
        #loadingIndicator {
            margin: 50px auto;
            text-align: center;
        }

        /* 选择模式样式 */
        .selection-mode .gallery-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .selection-mode .gallery-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .gallery-card.selected {
            border: 3px solid #007bff;
            box-shadow: 0 0 15px rgba(0,123,255,0.3);
        }

        .selection-checkbox {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            padding: 5px;
        }

        .selection-toolbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 10px 20px;
            display: none;
            z-index: 1000;
            border: 1px solid #ddd;
        }

        .selection-toolbar.show {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .selection-count {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="btn btn-primary back-btn">
        <i class="fas fa-arrow-left me-1"></i>返回首页
    </a>
    
    <!-- 刷新按钮 -->
    <button class="btn btn-success refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt me-1"></i>刷新
    </button>

    <!-- 选择模式按钮 -->
    <button class="btn btn-info" id="toggleSelectionBtn" onclick="toggleSelectionMode()" style="position: fixed; top: 20px; right: 180px; z-index: 1000;">
        <i class="fas fa-check-square me-1"></i>选择模式
    </button>

    <div class="container">
        <!-- 页面标题 -->
        <div class="gallery-header">
            <h1><i class="fas fa-images me-2"></i>画廊</h1>
            {% if current_user.get('is_admin') or current_user.get('is_authorized') %}
            <p class="text-muted mb-3">
                <i class="fas fa-crown text-warning me-1"></i>
                您是授权用户，可以查看所有用户的精彩作品
            </p>
            {% else %}
            <p class="text-muted mb-3">
                <i class="fas fa-user text-info me-1"></i>
                您当前只能查看自己的作品
            </p>
            {% endif %}

            <!-- 搜索和筛选控件 -->
            <div class="row justify-content-center mb-3">
                <div class="col-md-3">
                    <select id="timeRange" class="form-select form-select-sm">
                        <option value="1" selected>最近1小时</option>
                        <option value="3">最近3小时</option>
                        <option value="6">最近6小时</option>
                        <option value="12">最近12小时</option>
                        <option value="24">最近24小时</option>
                        <option value="72">最近3天</option>
                        <option value="168">最近7天</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="input-group input-group-sm">
                        {% if current_user.get('is_admin') or current_user.get('is_authorized') %}
                        <input type="text" id="searchInput" class="form-control" placeholder="搜索提示词、用户名或模型...">
                        {% else %}
                        <input type="text" id="searchInput" class="form-control" placeholder="搜索提示词或模型...">
                        {% endif %}
                        <button id="clearSearch" class="btn btn-outline-secondary" type="button" title="清除搜索">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select id="pageSize" class="form-select form-select-sm">
                        <option value="18">每页18个</option>
                        <option value="21">每页21个</option>
                        <option value="24">每页24个</option>
                        <option value="36">每页36个</option>
                        <option value="42" selected>每页42个</option>
                        <option value="63">每页63个</option>
                        <option value="84">每页84个</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button id="loadGallery" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <small class="text-muted">
                    共 <span id="totalCount">0</span> 个作品，
                    当前显示 <span id="recordCount">0</span> 个
                </small>
                <div id="paginationInfo" class="text-muted small" style="display: none;">
                    第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载画廊内容...</p>
        </div>

        <!-- 画廊内容容器 -->
        <div id="galleryContainer">
            <!-- 初始内容将通过JavaScript加载 -->
            <div class="no-records">
                <i class="fas fa-images fa-3x mb-3 text-muted"></i>
                <h3>正在加载作品...</h3>
                <p>请稍候，正在获取最新的作品展示</p>
            </div>
        </div>

        <!-- 分页控件 -->
        <div id="paginationContainer" class="d-flex justify-content-center mt-4" style="display: none !important;">
            <nav aria-label="画廊分页">
                <ul class="pagination pagination-sm" id="paginationList">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 选择工具栏 -->
    <div class="selection-toolbar" id="selectionToolbar">
        <span class="selection-count" id="selectionCount">已选择 0 项</span>
        <button class="btn btn-primary btn-sm" onclick="createShareLink()">
            <i class="fas fa-share-alt me-1"></i>生成分享链接
        </button>
        <button class="btn btn-secondary btn-sm" onclick="clearSelection()">
            <i class="fas fa-times me-1"></i>清除选择
        </button>
        <button class="btn btn-outline-secondary btn-sm" onclick="toggleSelectionMode()">
            <i class="fas fa-times me-1"></i>退出选择
        </button>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" alt="预览图片">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="modalImageLink" href="#" target="_blank" class="btn btn-primary">查看原图</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分享链接创建模态框 -->
    <div class="modal fade" id="shareModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-share-alt me-2"></i>创建分享链接
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="shareForm">
                        <div class="mb-3">
                            <label for="shareTitle" class="form-label">分享标题</label>
                            <input type="text" class="form-control" id="shareTitle" placeholder="输入分享标题（可选）">
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            将要分享 <span id="shareImageCount">0</span> 张图片
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>分享设置：</strong>
                            <ul class="mb-0 mt-2">
                                <li>有效期：7天后自动过期</li>
                                <li>已登录用户：可直接访问分享内容</li>
                                <li>未登录用户：需要输入访问秘钥才能查看</li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitShareLink()">
                        <i class="fas fa-share-alt me-1"></i>创建分享链接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分享结果模态框 -->
    <div class="modal fade" id="shareResultModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle text-success me-2"></i>分享链接创建成功
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">分享链接</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shareUrl" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyShareUrl()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">访问秘钥</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="accessKey" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyAccessKey()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <small class="text-muted">未登录用户需要此秘钥才能访问分享内容，已登录用户可直接访问</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">完成</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化图片预览功能
        function initImagePreview() {
            document.querySelectorAll('.gallery-image').forEach(img => {
                img.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
                    const modalImage = document.getElementById('modalImage');
                    const modalLink = document.getElementById('modalImageLink');

                    modalImage.src = this.src;
                    modalLink.href = this.src;
                    modal.show();
                });
            });
        }

        // 提示词展开/收起功能
        function togglePrompt(toggleElement) {
            const promptElement = toggleElement.previousElementSibling;
            const isCollapsed = promptElement.classList.contains('collapsed');

            if (isCollapsed) {
                promptElement.classList.remove('collapsed');
                toggleElement.textContent = '收起';
            } else {
                promptElement.classList.add('collapsed');
                toggleElement.textContent = '展开';
            }
        }

        // 格式化时间显示
        function formatTimeDisplay() {
            document.querySelectorAll('.time-info').forEach(timeElement => {
                const timeText = timeElement.textContent.trim();
                const timeMatch = timeText.match(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/);
                if (timeMatch) {
                    const timestamp = new Date(timeMatch[1]);
                    const now = new Date();
                    const diff = Math.floor((now - timestamp) / 1000 / 60); // 分钟差

                    let relativeTime;
                    if (diff < 1) {
                        relativeTime = '刚刚';
                    } else if (diff < 60) {
                        relativeTime = `${diff}分钟前`;
                    } else if (diff < 1440) {
                        relativeTime = `${Math.floor(diff / 60)}小时前`;
                    } else {
                        relativeTime = `${Math.floor(diff / 1440)}天前`;
                    }

                    timeElement.innerHTML = `<i class="fas fa-clock me-1"></i>${relativeTime}`;
                    timeElement.title = timeMatch[1]; // 鼠标悬停显示完整时间
                }
            });
        }

        // 全局变量
        let currentPage = 1;
        let currentSearch = '';
        let currentHours = 1;
        let currentPageSize = 42;
        let selectionMode = false;
        let selectedImages = new Set();

        // 加载画廊数据
        async function loadGalleryData() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const galleryContainer = document.getElementById('galleryContainer');
            const recordCount = document.getElementById('recordCount');
            const totalCount = document.getElementById('totalCount');
            const paginationContainer = document.getElementById('paginationContainer');

            // 获取当前参数
            currentHours = parseInt(document.getElementById('timeRange').value);
            currentSearch = document.getElementById('searchInput').value.trim();
            currentPageSize = parseInt(document.getElementById('pageSize').value);

            try {
                loadingIndicator.style.display = 'block';
                galleryContainer.style.display = 'none';
                paginationContainer.style.display = 'none';

                const params = new URLSearchParams({
                    hours: currentHours,
                    limit: currentPageSize,
                    page: currentPage,
                    search: currentSearch
                });

                const response = await fetch(`/api/gallery?${params}`);
                const data = await response.json();

                if (data.success) {
                    renderGallery(data.records);
                    recordCount.textContent = data.count;
                    totalCount.textContent = data.total_count;

                    // 更新分页信息
                    updatePaginationInfo(data);

                    // 显示分页控件（如果有多页）
                    if (data.total_pages > 1) {
                        renderPagination(data);
                        paginationContainer.style.display = 'block';
                    }
                } else {
                    throw new Error('加载失败');
                }
            } catch (error) {
                console.error('加载画廊数据失败:', error);
                galleryContainer.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载失败，请稍后重试
                    </div>
                `;
            } finally {
                loadingIndicator.style.display = 'none';
                galleryContainer.style.display = 'block';
            }
        }

        // 兼容旧的loadGallery函数
        async function loadGallery(hours = 1) {
            document.getElementById('timeRange').value = hours;
            currentPage = 1;
            await loadGalleryData();
        }

        // 更新分页信息
        function updatePaginationInfo(data) {
            const currentPageSpan = document.getElementById('currentPage');
            const totalPagesSpan = document.getElementById('totalPages');
            const paginationInfo = document.getElementById('paginationInfo');

            if (currentPageSpan) currentPageSpan.textContent = data.page;
            if (totalPagesSpan) totalPagesSpan.textContent = data.total_pages;

            if (data.total_pages > 1) {
                paginationInfo.style.display = 'block';
            } else {
                paginationInfo.style.display = 'none';
            }
        }

        // 渲染分页控件
        function renderPagination(data) {
            const paginationList = document.getElementById('paginationList');
            let html = '';

            // 上一页按钮
            if (data.has_prev) {
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="goToPage(${data.page - 1}); return false;">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                `;
            } else {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                    </li>
                `;
            }

            // 页码按钮
            const startPage = Math.max(1, data.page - 2);
            const endPage = Math.min(data.total_pages, data.page + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1); return false;">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === data.page) {
                    html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${i}); return false;">${i}</a></li>`;
                }
            }

            if (endPage < data.total_pages) {
                if (endPage < data.total_pages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${data.total_pages}); return false;">${data.total_pages}</a></li>`;
            }

            // 下一页按钮
            if (data.has_next) {
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="goToPage(${data.page + 1}); return false;">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                `;
            } else {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                    </li>
                `;
            }

            paginationList.innerHTML = html;
        }

        // 跳转到指定页面
        function goToPage(page) {
            currentPage = page;
            loadGalleryData();
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            currentPage = 1;
            loadGalleryData();
        }

        // 渲染画廊内容
        function renderGallery(records) {
            const galleryContainer = document.getElementById('galleryContainer');

            if (records.length === 0) {
                galleryContainer.innerHTML = `
                    <div class="no-records">
                        <i class="fas fa-images fa-3x mb-3 text-muted"></i>
                        <h3>暂无作品</h3>
                        <p>选定时间范围内还没有用户生成作品，快去<a href="/">首页</a>创作吧！</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row" id="galleryGrid">';
            records.forEach(record => {
                html += renderRecordCard(record);
            });
            html += '</div>';

            galleryContainer.innerHTML = html;

            // 重新初始化功能
            initImagePreview();
            formatTimeDisplay();
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 渲染单个记录卡片
        function renderRecordCard(record) {
            let mediaHtml = '';
            let badgeHtml = '';
            let buttonHtml = '';

            if (record.type === 'image' && record.image_url) {
                mediaHtml = `
                    <img src="${escapeHtml(record.image_url)}" class="gallery-image" alt="生成的图片" loading="lazy"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="placeholder-image" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-image fa-2x mb-2"></i><br>
                            图片加载失败
                        </div>
                    </div>
                `;
                badgeHtml = '<span class="badge bg-primary type-badge">图片</span>';
                buttonHtml = `
                    <a href="${escapeHtml(record.image_url)}" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>查看原图
                    </a>
                `;
            } else if (record.type === 'video' && record.video_url) {
                mediaHtml = `
                    <video class="gallery-video" controls preload="metadata">
                        <source src="${escapeHtml(record.video_url)}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                `;
                badgeHtml = '<span class="badge bg-success type-badge">视频</span>';
                buttonHtml = `
                    <a href="${escapeHtml(record.video_url)}" target="_blank" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>查看原视频
                    </a>
                `;
            } else {
                mediaHtml = `
                    <div class="placeholder-image">
                        <div class="text-center">
                            <i class="fas fa-question fa-2x mb-2"></i><br>
                            内容不可用
                        </div>
                    </div>
                `;
                badgeHtml = '<span class="badge bg-secondary type-badge">未知</span>';
            }

            // 处理提示词展开/收起
            const promptToggleHtml = record.prompt && record.prompt.length > 150 ?
                `<div class="prompt-toggle" onclick="togglePrompt(this)">展开</div>` : '';

            const negativePromptToggleHtml = record.negative_prompt && record.negative_prompt.length > 120 ?
                `<div class="prompt-toggle" onclick="togglePrompt(this)">展开</div>` : '';

            const negativePromptHtml = record.negative_prompt ? `
                <div class="mb-2">
                    <strong>负面提示词：</strong>
                    <div class="negative-prompt-text collapsed" data-full-text="${escapeHtml(record.negative_prompt)}">${escapeHtml(record.negative_prompt)}</div>
                    ${negativePromptToggleHtml}
                </div>
            ` : '';

            const modelHtml = record.model_name ? `
                <div class="model-info mb-2">
                    <i class="fas fa-cog me-1"></i>${escapeHtml(record.model_name)}
                </div>
            ` : '';

            return `
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="card gallery-card">
                        <div class="card-img-container">
                            ${mediaHtml}
                            ${badgeHtml}
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="user-info">
                                    <i class="fas fa-user me-1"></i>${escapeHtml(record.username)}
                                </span>
                                <span class="time-info">
                                    <i class="fas fa-clock me-1"></i>${record.timestamp.substring(0, 19).replace('T', ' ')}
                                </span>
                            </div>
                            ${modelHtml}
                            <div class="mb-2">
                                <strong>提示词：</strong>
                                <div class="prompt-text collapsed" data-full-text="${escapeHtml(record.prompt)}">${escapeHtml(record.prompt)}</div>
                                ${promptToggleHtml}
                            </div>
                            ${negativePromptHtml}
                            <div class="d-grid">
                                ${buttonHtml}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 页面初始加载时就使用分页API，而不是服务器端渲染的数据
            loadGalleryData();

            // 绑定搜索和筛选事件
            document.getElementById('loadGallery').addEventListener('click', function() {
                currentPage = 1; // 重置到第一页
                loadGalleryData();
            });

            document.getElementById('clearSearch').addEventListener('click', clearSearch);

            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    currentPage = 1; // 重置到第一页
                    loadGalleryData();
                }
            });

            // 绑定页面大小变化事件
            document.getElementById('pageSize').addEventListener('change', function() {
                currentPage = 1; // 重置到第一页
                loadGalleryData();
            });

            // 绑定刷新按钮事件（如果存在）
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    currentPage = 1;
                    loadGalleryData();

                    // 重置按钮样式
                    this.classList.remove('btn-warning');
                    this.classList.add('btn-success');
                    this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
                });
            }
        });

        // 添加刷新提示
        let lastRefresh = Date.now();
        setInterval(() => {
            const elapsed = Math.floor((Date.now() - lastRefresh) / 1000);
            if (elapsed > 300) { // 5分钟后提示刷新
                const refreshBtn = document.querySelector('.refresh-btn');
                if (refreshBtn && !refreshBtn.classList.contains('btn-warning')) {
                    refreshBtn.classList.remove('btn-success');
                    refreshBtn.classList.add('btn-warning');
                    refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>有新内容';
                }
            }
        }, 30000); // 每30秒检查一次

        // 选择模式相关功能
        function toggleSelectionMode() {
            selectionMode = !selectionMode;
            const toggleBtn = document.getElementById('toggleSelectionBtn');
            const galleryContainer = document.getElementById('galleryContainer');
            const selectionToolbar = document.getElementById('selectionToolbar');

            if (selectionMode) {
                // 进入选择模式
                toggleBtn.innerHTML = '<i class="fas fa-times me-1"></i>退出选择';
                toggleBtn.classList.remove('btn-info');
                toggleBtn.classList.add('btn-warning');
                galleryContainer.classList.add('selection-mode');

                // 为每个图片卡片添加选择框
                addSelectionCheckboxes();
            } else {
                // 退出选择模式
                toggleBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>选择模式';
                toggleBtn.classList.remove('btn-warning');
                toggleBtn.classList.add('btn-info');
                galleryContainer.classList.remove('selection-mode');
                selectionToolbar.classList.remove('show');

                // 清除选择
                clearSelection();
                removeSelectionCheckboxes();
            }
        }

        function addSelectionCheckboxes() {
            document.querySelectorAll('.gallery-card').forEach((card, index) => {
                // 避免重复添加
                if (card.querySelector('.selection-checkbox')) return;

                const checkbox = document.createElement('div');
                checkbox.className = 'selection-checkbox';
                checkbox.innerHTML = '<input type="checkbox" class="form-check-input">';

                const cardImgContainer = card.querySelector('.card-img-container');
                if (cardImgContainer) {
                    cardImgContainer.style.position = 'relative';
                    cardImgContainer.appendChild(checkbox);
                }

                // 添加点击事件
                card.addEventListener('click', function(e) {
                    if (selectionMode) {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleImageSelection(card, index);
                    }
                });
            });
        }

        function removeSelectionCheckboxes() {
            document.querySelectorAll('.selection-checkbox').forEach(checkbox => {
                checkbox.remove();
            });
        }

        function toggleImageSelection(card, index) {
            const checkbox = card.querySelector('.selection-checkbox input');
            const isSelected = selectedImages.has(index);

            if (isSelected) {
                selectedImages.delete(index);
                card.classList.remove('selected');
                checkbox.checked = false;
            } else {
                selectedImages.add(index);
                card.classList.add('selected');
                checkbox.checked = true;
            }

            updateSelectionToolbar();
        }

        function updateSelectionToolbar() {
            const selectionToolbar = document.getElementById('selectionToolbar');
            const selectionCount = document.getElementById('selectionCount');

            if (selectedImages.size > 0) {
                selectionToolbar.classList.add('show');
                selectionCount.textContent = `已选择 ${selectedImages.size} 项`;
            } else {
                selectionToolbar.classList.remove('show');
            }
        }

        function clearSelection() {
            selectedImages.clear();
            document.querySelectorAll('.gallery-card').forEach(card => {
                card.classList.remove('selected');
                const checkbox = card.querySelector('.selection-checkbox input');
                if (checkbox) checkbox.checked = false;
            });
            updateSelectionToolbar();
        }

        function createShareLink() {
            if (selectedImages.size === 0) {
                alert('请先选择要分享的图片');
                return;
            }

            // 更新模态框中的图片数量
            document.getElementById('shareImageCount').textContent = selectedImages.size;

            // 显示分享模态框
            const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
            shareModal.show();
        }

        async function submitShareLink() {
            const title = document.getElementById('shareTitle').value.trim();
            const expireDays = 7; // 固定7天过期
            const requireKey = true; // 固定需要秘钥

            if (selectedImages.size === 0) {
                alert('请先选择要分享的图片');
                return;
            }

            // 收集选中的图片数据
            const selectedImageData = [];
            const galleryCards = document.querySelectorAll('.gallery-card');

            selectedImages.forEach(index => {
                const card = galleryCards[index];
                if (card) {
                    const img = card.querySelector('.gallery-image');
                    const promptElement = card.querySelector('.prompt-text');
                    const userElement = card.querySelector('.user-info');
                    const timeElement = card.querySelector('.time-info');
                    const modelElement = card.querySelector('.model-info');

                    if (img) {
                        selectedImageData.push({
                            image_url: img.src,
                            prompt: promptElement ? promptElement.getAttribute('data-full-text') || promptElement.textContent : '',
                            username: userElement ? userElement.textContent.replace('👤', '').trim() : '',
                            timestamp: timeElement ? timeElement.getAttribute('title') || timeElement.textContent : '',
                            model_name: modelElement ? modelElement.textContent : ''
                        });
                    }
                }
            });

            try {
                const response = await fetch('/api/create_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: title,
                        images: selectedImageData,
                        expire_days: expireDays,
                        require_key: requireKey
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 隐藏创建模态框
                    const shareModal = bootstrap.Modal.getInstance(document.getElementById('shareModal'));
                    shareModal.hide();

                    // 显示结果模态框
                    document.getElementById('shareUrl').value = data.share_url;
                    document.getElementById('accessKey').value = data.access_key;

                    const shareResultModal = new bootstrap.Modal(document.getElementById('shareResultModal'));
                    shareResultModal.show();

                    // 清除选择并退出选择模式
                    clearSelection();
                    toggleSelectionMode();
                } else {
                    alert('创建分享链接失败：' + data.message);
                }
            } catch (error) {
                console.error('创建分享链接失败:', error);
                alert('创建分享链接失败，请稍后重试');
            }
        }

        function copyShareUrl() {
            const shareUrl = document.getElementById('shareUrl');
            shareUrl.select();
            document.execCommand('copy');

            // 临时改变按钮文本
            const btn = event.target.closest('button');
            const originalHtml = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                btn.innerHTML = originalHtml;
            }, 1000);
        }

        function copyAccessKey() {
            const accessKey = document.getElementById('accessKey');
            accessKey.select();
            document.execCommand('copy');

            // 临时改变按钮文本
            const btn = event.target.closest('button');
            const originalHtml = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                btn.innerHTML = originalHtml;
            }, 1000);
        }
    </script>
</body>
</html>
